<script>
  import { onMount } from 'svelte'
  import { currentUser } from '../stores/authStore.js'
  import {
    addItemToInProgressSale,
    completeInProgressSale,
    createInProgressSale,
    searchInventory as dbSearchInventory,
    generateSalesNumber,
    getTodaysInProgressSale,
    saveSaleTransaction,
  } from '../utils/database.js'
  import { showError, showInfo, showSuccess } from '../utils/toastUtils.js'
  import './Home.css'
  // Component state
  let searchTerm = ''
  let isSearching = false
  let salesItems = []
  let searchInput
  let sequenceNumber = 1
  let showDLSProducts = false

  // Sales management state
  let currentSale = null
  let currentSaleUuid = null
  let isInProgressSale = false

  // ULTRA BASIT Autocomplete
  let searchResults = []
  let showDropdown = false
  let selectedIndex = -1
  let searchTimeout = null

  function updateCustomerScreen() {
    if (window.electronAPI && window.electronAPI.updateCustomerScreen) {
      window.electronAPI.updateCustomerScreen(salesItems)
    }

    try {
      const customerWindow = window.open('', 'CustomerScreen')
      if (customerWindow) {
        customerWindow.postMessage(
          {
            type: 'SALES_UPDATE',
            items: salesItems,
          },
          '*'
        )
      }
    } catch {
      // ignore
    }
  }

  $: if (salesItems) {
    updateCustomerScreen()
  }

  // Payment system variables
  let paymentAmount = '₺0,00'
  let rawPaymentAmount = 0
  let selectedPaymentMethod = ''

  // Partial payment system
  let partialPayments = [] // Array to store partial payments
  let totalPaidAmount = 0 // Total amount paid so far
  let remainingAmount = 0 // Remaining amount to be paid

  // Cash payment change screen
  let showCashChangeModal = false
  let showPaymentMethodModal = false // New modal for payment method selection
  let cashReceived = 0
  let changeAmount = 0

  // UTILITY FUNCTIONS
  function formatTurkishCurrency(amount) {
    const numericAmount = parseFloat(amount) || 0
    return numericAmount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  function formatQuantity(quantity) {
    return quantity.toLocaleString('tr-TR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })
  }

  function parseQuantityInput(input) {
    return String(input).replace(',', '.')
  }

  // CustomerScreen'i yeni pencerede aç
  async function openCustomerScreen() {
    try {
      // Electron uygulaması için IPC kullanarak yeni pencere aç
      if (window.electronAPI && window.electronAPI.openCustomerWindow) {
        console.log('🪟 Electron API ile customer window açılıyor...')
        const result = await window.electronAPI.openCustomerWindow()
        if (result.success) {
          console.log('✅ Customer window başarıyla açıldı')
        } else {
          console.error('❌ Customer window açılamadı:', result.message)
        }
      } else {
        // Fallback: Aynı pencerede yeni tab olarak aç
        console.log('Electron API bulunamadı, alternatif çözüm kullanılıyor')
        // URL'yi yeni tab'da aç (popup blocker'ı bypass eder)
        const customerUrl = `${window.location.origin}/#/customer`
        const newTab = window.open(customerUrl, '_blank')
        if (!newTab) {
          console.warn('Popup blocked, manuel olarak /customer adresine gidin')
        }
      }
    } catch (error) {
      console.error('❌ Customer window açma hatası:', error)
    }
  }

  function isDecimalUnit(unit) {
    const decimalUnits = ['kg', 'KG', 'Kg', 'kG']
    return decimalUnits.includes(unit)
  }

  function validateQuantityInput(input, unit) {
    if (isDecimalUnit(unit)) {
      const decimalPattern = /^\d+([,]\d{0,2})?$/
      return decimalPattern.test(input)
    } else {
      const integerPattern = /^\d+$/
      return integerPattern.test(input)
    }
  }

  function getQuantityStep(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function getMinimumQuantity(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function handleQuantityKeydown(event, unit) {
    const key = event.key
    const currentValue = event.target.value
    const controlKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Home',
      'End',
    ]

    if (controlKeys.includes(key)) return
    if (event.ctrlKey && ['a', 'c', 'v', 'x'].includes(key.toLowerCase())) return

    if (isDecimalUnit(unit)) {
      if (/^\d$/.test(key)) return
      if (key === ',' && !currentValue.includes(',')) return
    } else {
      if (/^\d$/.test(key)) return
    }
    event.preventDefault()
  }

  function updatePaymentDisplay() {
    if (rawPaymentAmount === 0) {
      paymentAmount = '₺0,00'
    } else {
      paymentAmount = formatTurkishCurrency(rawPaymentAmount / 100)
    }
  }

  // TABS
  let activeTab = 'search'
  function changeTab(tabName) {
    activeTab = tabName
  }

  // ULTRA BASIT ARAMA
  function handleInputChange() {
    console.log('🔍 handleInputChange çağrıldı, searchTerm:', searchTerm)
    if (searchTimeout) clearTimeout(searchTimeout)

    if (searchTerm.length < 2) {
      console.log('⚠️ Arama terimi çok kısa, dropdown kapatılıyor')
      closeDropdown()
      return
    }

    console.log('⏱️ Arama timeout başlatılıyor...')
    searchTimeout = setTimeout(() => {
      performSearch()
    }, 300)
  }

  async function performSearch() {
    console.log('🚀 performSearch başlatıldı, searchTerm:', searchTerm)
    console.log('🔍 window.electronAPI mevcut mu?', !!window.electronAPI)
    console.log('🔍 dbSearchInventory fonksiyonu mevcut mu?', !!dbSearchInventory)

    if (searchTerm.length < 2) return

    // Focus'u koru
    const inputWasFocused = document.activeElement === searchInput

    isSearching = true
    console.log('🔄 isSearching = true, API çağrısı yapılıyor...')
    try {
      const results = await dbSearchInventory(searchTerm)
      console.log('✅ API sonucu alındı:', results)
      searchResults = results ? results.slice(0, 5) : []

      if (searchResults.length > 0) {
        console.log('📋 Sonuçlar bulundu, dropdown açılıyor...')
        openDropdown()
        // Focus'u geri ver
        if (inputWasFocused && searchInput) {
          setTimeout(() => {
            searchInput.focus()
          }, 0)
        }
      } else {
        console.log('❌ Sonuç bulunamadı, dropdown kapatılıyor')
        closeDropdown()
      }
    } catch (error) {
      console.error('💥 Arama hatası:', error)
      closeDropdown()
    }
    isSearching = false
    console.log('✅ performSearch tamamlandı, isSearching = false')
  }

  // Input blur olayını kontrol et - dropdown açıksa focus'u geri ver
  function handleInputBlur(_) {
    // Eğer dropdown açık ve blur dropdown'dan kaynaklanıyorsa focus'u geri ver
    if (showDropdown) {
      setTimeout(() => {
        if (searchInput && !searchInput.matches(':focus')) {
          searchInput.focus()
        }
      }, 10)
    }
  }

  // DROPDOWN PORTAL SYSTEM - BODY'YE EKLER
  let dropdownElement = null
  let inputRect = null

  function openDropdown() {
    closeDropdown() // Önce varsa kapat

    if (!searchInput || searchResults.length === 0) return

    // Input focus'unu koru
    const wasInputFocused = document.activeElement === searchInput

    // Input pozisyonunu al
    inputRect = searchInput.getBoundingClientRect()

    // Dropdown element oluştur
    dropdownElement = document.createElement('div')
    dropdownElement.style.cssText = `
      position: fixed;
      top: ${inputRect.bottom + 4}px;
      left: ${inputRect.left}px;
      width: ${inputRect.width}px;
      background: white;
      border: 2px solid #1e3a8a;
      border-radius: 8px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      z-index: 99999;
      max-height: 300px;
      overflow-y: auto;
    `

    // İçeriği ekle
    dropdownElement.innerHTML = searchResults
      .map(
        (item, index) => `
      <div class="dropdown-item" data-index="${index}" style="
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        ${index === selectedIndex ? 'background: #f8f9fb; border-left: 4px solid #1e3a8a;' : ''}
      ">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">${item.name}</div>
        <div style="display: flex; gap: 0.5rem; font-size: 0.8rem;">
          <span style="background: #f1f5f9; padding: 0.2rem 0.5rem; border-radius: 4px;">Kod: ${item.inventory_code}</span>
          <span style="background: #1e3a8a; color: white; padding: 0.2rem 0.5rem; border-radius: 4px;">
            ${item.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
          </span>
        </div>
      </div>
    `
      )
      .join('')

    // Click eventleri ekle - mousedown ile focus kaybını engelle
    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      // Mouse down'da focus kaybını engelle
      el.addEventListener('mousedown', e => {
        e.preventDefault() // Input'un blur olmasını engelle
      })

      el.addEventListener('click', e => {
        e.preventDefault()
        selectItem(searchResults[index])
      })
    })

    // Body'ye ekle
    document.body.appendChild(dropdownElement)
    showDropdown = true
    selectedIndex = -1

    // Input focus'unu geri ver
    if (wasInputFocused) {
      setTimeout(() => {
        searchInput?.focus()
      }, 0)
    }
  }

  function closeDropdown() {
    if (dropdownElement) {
      document.body.removeChild(dropdownElement)
      dropdownElement = null
    }
    showDropdown = false
    selectedIndex = -1
  }

  function updateDropdownSelection() {
    if (!dropdownElement) return

    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      if (index === selectedIndex) {
        el.style.background = '#f8f9fb'
        el.style.borderLeft = '4px solid #1e3a8a'
        el.scrollIntoView({ block: 'nearest' })
      } else {
        el.style.background = 'white'
        el.style.borderLeft = 'none'
      }
    })
  }

  async function selectItem(item) {
    closeDropdown()
    await addItemToSales(item)
    searchTerm = ''
    searchInput?.focus()
  }

  // KLAVYE NAVİGASYONU
  function handleKeyDown(event) {
    // ENTER tuşu için özel kontrol - dropdown açık olmasa da çalışsın
    if (event.key === 'Enter') {
      event.preventDefault()

      // Eğer dropdown açık ve sonuçlar varsa
      if (showDropdown && searchResults.length > 0) {
        if (selectedIndex >= 0) {
          selectItem(searchResults[selectedIndex])
        } else {
          selectItem(searchResults[0])
        }
      }
      // Eğer dropdown kapalı ama arama terimi varsa, arama yap ve ilk sonucu ekle
      else if (searchTerm.trim().length >= 2) {
        performSearchAndAddFirst()
      }
      return
    }

    // Diğer tuşlar için dropdown açık olmalı
    if (!showDropdown || searchResults.length === 0) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectedIndex = selectedIndex < searchResults.length - 1 ? selectedIndex + 1 : 0
        updateDropdownSelection()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : searchResults.length - 1
        updateDropdownSelection()
        break
      case 'Escape':
        closeDropdown()
        break
    }
  }

  // ENTER ile arama yap ve ilk sonucu ekle
  async function performSearchAndAddFirst() {
    if (isSearching || !searchTerm.trim()) return

    console.log('🎯 performSearchAndAddFirst çalışıyor, searchTerm:', searchTerm.trim())
    isSearching = true
    try {
      const results = await dbSearchInventory(searchTerm.trim())
      console.log('🔍 Arama sonuçları:', results)

      if (results && results.length > 0) {
        console.log('✅ İlk sonuç:', results[0])
        console.log('🚀 addItemToSales çağrılıyor...')
        await addItemToSales(results[0])
        searchTerm = ''
        searchInput?.focus()
        console.log('✅ Ürün başarıyla eklendi ve arama temizlendi')
      } else {
        console.log('❌ Hiç sonuç bulunamadı')
        showError('Ürün bulunamadı')
      }
    } catch (error) {
      console.error('💥 Arama hatası:', error)
      showError('Arama sırasında hata oluştu')
    } finally {
      isSearching = false
      console.log('✅ performSearchAndAddFirst tamamlandı')
    }
  }

  // CLICK OUTSIDE TO CLOSE
  function handleClickOutside(event) {
    if (
      showDropdown &&
      searchInput &&
      !searchInput.contains(event.target) &&
      dropdownElement &&
      !dropdownElement.contains(event.target)
    ) {
      closeDropdown()
    }
  }

  // BARCODE SCANNER
  let barcodeBuffer = ''
  let barcodeTimeout = null

  function handleGlobalKeyDown(event) {
    const target = event.target
    const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA'

    if (!isInputField && event.key.length === 1) {
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      barcodeBuffer += event.key
      barcodeTimeout = setTimeout(() => {
        if (barcodeBuffer.length >= 3) {
          searchTerm = barcodeBuffer
          performSearch()
        }
        barcodeBuffer = ''
      }, 100)
    }
  }

  // SALES MANAGEMENT
  async function addItemToSales(item) {
    console.log('🛒 addItemToSales çağrıldı')
    console.log('📦 Eklenecek item:', item)
    console.log('📋 Mevcut salesItems:', salesItems)

    if (!item || !item.id) {
      showError('Geçersiz ürün bilgisi!')
      return
    }

    const existingItemIndex = salesItems.findIndex(
      salesItem => salesItem.id === item.id || salesItem.inventory_code === item.inventory_code
    )

    console.log('🔍 existingItemIndex:', existingItemIndex)

    if (existingItemIndex !== -1) {
      console.log('♻️ Mevcut ürün bulundu, miktarı artırılıyor')
      incrementExistingItem(existingItemIndex, item)
    } else {
      console.log('🆕 Yeni ürün ekleniyor')
      await addNewItemToSales(item)
    }

    // Add item to database if in progress sale exists
    if (currentSaleUuid) {
      addItemToCurrentSale(item)
    }

    console.log('✅ İşlem sonrası salesItems:', salesItems)
  }

  function incrementExistingItem(existingItemIndex, item) {
    const updatedSalesItems = [...salesItems]
    const existingItem = updatedSalesItems[existingItemIndex]
    const increment = getQuantityStep(existingItem.unit)
    existingItem.quantity = Math.round((existingItem.quantity + increment) * 100) / 100
    existingItem.total = existingItem.quantity * existingItem.price
    salesItems = updatedSalesItems
    showSuccess(
      `${item.name} miktarı artırıldı (${formatQuantity(existingItem.quantity)} ${existingItem.unit})`
    )
  }

  async function addNewItemToSales(item) {
    // Check if this is the first item being added (empty sales list)
    if (salesItems.length === 0) {
      await createNewSaleRecord()
    }

    const initialQuantity = getMinimumQuantity(item.unit || 'adet')
    const itemPrice = parseFloat(item.price) || 0
    const salesItem = {
      sequenceNo: sequenceNumber++,
      id: item.id,
      name: item.name,
      unit: item.unit || 'adet',
      price: itemPrice,
      original_price: itemPrice,
      inventory_code: item.inventory_code,
      barcode: item.barcode || '',
      quantity: initialQuantity,
      total: itemPrice * initialQuantity,
    }
    salesItems = [...salesItems, salesItem]
    showSuccess(`${item.name} satış listesine eklendi`)
  }

  function removeItem(index) {
    const itemToRemove = salesItems[index]
    salesItems = salesItems.filter((_, i) => i !== index)
    reorderSequenceNumbers(index)
    showSuccess(`${itemToRemove.name} satış listesinden kaldırıldı`)
  }

  function reorderSequenceNumbers(removedIndex) {
    if (removedIndex === 0) {
      salesItems = salesItems.map(item => ({ ...item, sequenceNo: item.sequenceNo - 1 }))
      sequenceNumber = sequenceNumber - 1
    } else {
      salesItems = salesItems.map(item => ({
        ...item,
        sequenceNo: item.sequenceNo > removedIndex + 1 ? item.sequenceNo - 1 : item.sequenceNo,
      }))
      sequenceNumber = sequenceNumber - 1
    }
  }

  function increaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const increment = getQuantityStep(item.unit)
    item.quantity = Math.round((item.quantity + increment) * 100) / 100
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
    showSuccess(`${item.name} miktarı artırıldı (${formatQuantity(item.quantity)} ${item.unit})`)
  }

  function decreaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const decrement = getQuantityStep(item.unit)
    const minQuantity = getMinimumQuantity(item.unit)
    const newQuantity = Math.round((item.quantity - decrement) * 100) / 100

    if (newQuantity >= minQuantity) {
      item.quantity = newQuantity
      item.total = item.quantity * item.price
      salesItems = updatedSalesItems
      showSuccess(`${item.name} miktarı azaltıldı (${formatQuantity(item.quantity)} ${item.unit})`)
    } else {
      removeItem(index)
    }
  }

  function handleQuantityChange(index, newQuantity) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]

    if (!validateQuantityInput(newQuantity, item.unit)) {
      showError(
        `${item.unit} birimi için ${isDecimalUnit(item.unit) ? 'ondalık değer (örn: 1,50)' : 'tam sayı'} giriniz`
      )
      return
    }

    const normalizedQuantity = parseQuantityInput(newQuantity)
    let quantity = parseFloat(normalizedQuantity)
    const minQuantity = getMinimumQuantity(item.unit)
    if (isNaN(quantity) || quantity <= 0) quantity = minQuantity
    quantity = Math.round(quantity * 100) / 100

    if (quantity < minQuantity) {
      removeItem(index)
      return
    }

    item.quantity = quantity
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
  }

  $: totalAmount = salesItems.reduce((sum, item) => sum + item.total, 0)
  $: remainingAmount = totalAmount - totalPaidAmount
  $: isPaymentComplete = remainingAmount <= 0 && totalAmount > 0

  // PAYMENT METHODS
  function addToAmount(digit) {
    rawPaymentAmount = rawPaymentAmount * 10 + (digit === '.' ? 0 : parseInt(digit))
    updatePaymentDisplay()
  }

  function removeLastDigit() {
    rawPaymentAmount = Math.floor(rawPaymentAmount / 10)
    updatePaymentDisplay()
  }

  function clearAmount() {
    rawPaymentAmount = 0
    updatePaymentDisplay()
  }

  function openCashChangeModal() {
    const currentPaymentAmount = rawPaymentAmount / 100 // Convert from cents to TL

    // If no keypad input, use remaining amount
    const paymentAmount = currentPaymentAmount > 0 ? currentPaymentAmount : remainingAmount

    cashReceived = paymentAmount
    changeAmount = Math.max(0, cashReceived - remainingAmount)
    showCashChangeModal = true

    console.log('💵 Para üstü modalı açılıyor:', {
      currentPaymentAmount,
      remainingAmount,
      cashReceived,
      changeAmount,
      hasKeypadInput: rawPaymentAmount > 0,
    })
  }

  function closeCashChangeModal() {
    showCashChangeModal = false
    cashReceived = 0
    changeAmount = 0
  }

  // Payment Method Modal Functions
  function openPaymentMethodModal() {
    showPaymentMethodModal = true
  }

  function closePaymentMethodModal() {
    showPaymentMethodModal = false
  }

  function selectPaymentMethodFromModal(method) {
    closePaymentMethodModal()

    if (method === 'cash') {
      // For cash, open the cash change modal
      openCashChangeModal()
    } else if (method === 'split') {
      // For split payment, show info that user can now select individual payment methods
      showInfo('Bölünmüş ödeme seçildi. Ödeme yöntemlerini seçerek tutarları ekleyebilirsiniz.')
    } else if (method === 'credit-card') {
      // Kredi kartı ödemesini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(remainingAmount)
    }
  }

  async function completeCashSale() {
    console.log('💵 Nakit satış tamamlanıyor:', {
      cashReceived,
      remainingAmount,
      changeAmount,
    })

    if (cashReceived < remainingAmount) {
      showError('Alınan nakit tutar yetersiz!')
      return
    }

    const actualPayment = Math.min(cashReceived, remainingAmount)
    const success = addPartialPayment('cash', actualPayment)

    if (success) {
      selectedPaymentMethod = ''
      rawPaymentAmount = 0
      updatePaymentDisplay()
      document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('is-active', 'is-selected')
      })

      if (changeAmount > 0) {
        showSuccess(
          `Nakit ödeme tamamlandı! Para üstü: ${changeAmount.toLocaleString('tr-TR', {
            style: 'currency',
            currency: 'TRY',
          })}`
        )
      } else {
        showSuccess('Nakit ödeme tamamlandı!')
      }

      closeCashChangeModal()

      // Check if payment is complete and complete sale
      if (isPaymentComplete) {
        await completeSale()
      }
    }
  }

  function selectPaymentMethod(method) {
    // Check if there are items in the cart
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    // Check if payment is already complete
    if (remainingAmount <= 0) {
      showError('Ödeme zaten tamamlanmış!')
      return
    }

    // Set the payment method as active in UI
    selectedPaymentMethod = method
    document.querySelectorAll('.payment-btn').forEach(btn => {
      btn.classList.remove('is-active', 'is-selected')
    })
    const selectedButton = document.querySelector(
      `.payment-btn.${method.toLowerCase().replace(/\s+/g, '-')}`
    )
    if (selectedButton) {
      selectedButton.classList.add('is-active', 'is-selected')
    }

    // Get the amount from keypad (convert from cents to TL)
    const keypadAmount = rawPaymentAmount / 100

    // Special handling for credit card - if no keypad amount, send remaining amount to POS
    if (keypadAmount <= 0) {
      if (method === 'credit-card') {
        handleCreditCardPOSWorkflow(remainingAmount)
        return
      } else {
        showInfo(
          `${getPaymentMethodDisplayName(method)} seçildi. Tutar girin veya "Kalanını Öde" butonunu kullanın.`
        )
        return
      }
    }

    // Validate payment amount doesn't exceed remaining amount
    if (keypadAmount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    // Process the payment based on method
    if (method === 'cash') {
      handleCashPaymentWorkflow(keypadAmount)
    } else if (method === 'credit-card') {
      // Kredi kartı ödemelerini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(keypadAmount)
    } else if (method === 'meal-card') {
      handleMealCardPaymentWorkflow(keypadAmount)
    }
  }

  // NEW SPLIT PAYMENT WORKFLOW FUNCTIONS
  function handleCashPaymentWorkflow(amount) {
    console.log('💵 Nakit ödeme workflow:', { amount, remainingAmount })

    // Add cash payment to the split payments list
    const success = addPartialPayment('cash', amount)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()
      showSuccess(`${amount.toFixed(2)} TL nakit ödeme eklendi`)

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Nakit ödeme tamamlandı!')
      } else {
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  function handleCreditCardPaymentWorkflow(amount) {
    console.log('💳 Kredi kartı ödeme workflow:', { amount, remainingAmount })

    // Add credit card payment to the split payments list
    const success = addPartialPayment('credit-card', amount)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()
      showSuccess(`${amount.toFixed(2)} TL kredi kartı ödemesi eklendi`)

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Kredi kartı ödemesi tamamlandı!')
      } else {
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  async function handleCreditCardPOSWorkflow(amount) {
    console.log('💳 Kredi kartı POS workflow başlatılıyor:', { amount, remainingAmount })

    // Validate payment amount
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    if (amount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    try {
      // Show loading message
      showInfo(`${amount.toFixed(2)} TL kredi kartı ödemesi POS'a gönderiliyor...`)

      // Clear keypad and reset UI immediately
      clearPaymentInput()

      // Send to POS for authorization FIRST
      console.log("🏧 Kredi kartı ödemesi POS'a gönderiliyor:", {
        amount,
        remainingAmount,
        totalAmount,
      })

      // Temporarily add payment amount to rawPaymentAmount for POS processing
      rawPaymentAmount = Math.round(amount * 100) // Convert to cents
      updatePaymentDisplay()

      // Send to POS system for authorization
      await sendRealSaleData()

      // If POS response is successful, add payment to split payments
      const success = addPartialPayment('credit-card', amount)

      if (success) {
        showSuccess(`${amount.toFixed(2)} TL kredi kartı ödemesi başarılı!`)

        // Check if payment is now complete
        if (isPaymentComplete) {
          showSuccess('Kredi kartı ödemesi tamamlandı!')
          // Don't auto-complete sale here - let user click Complete Sale button
        } else {
          showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
        }
      }
    } catch (error) {
      console.error('❌ Kredi kartı POS hatası:', error)
      showError(`Kredi kartı ödemesi başarısız: ${error.message}`)

      // Clear the payment input on error
      rawPaymentAmount = 0
      updatePaymentDisplay()
    }
  }

  // Function for credit card payment without immediate POS integration (for split payments)
  async function handleCreditCardPaymentOnly(amount) {
    console.log('💳 Kredi kartı ödeme (sadece liste):', { amount, remainingAmount })

    // Validate payment amount
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    if (amount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    // Add credit card payment to the split payments list without POS integration
    const success = addPartialPayment('credit-card', amount)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()
      showSuccess(`${amount.toFixed(2)} TL kredi kartı ödemesi eklendi`)

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Ödeme tamamlandı! "Satışı Tamamla" butonuna tıklayın.')
      } else {
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  function handleMealCardPaymentWorkflow(amount) {
    console.log('🍽️ Yemek kartı ödeme workflow:', { amount, remainingAmount })

    // Add meal card payment to the split payments list
    const success = addPartialPayment('meal-card', amount)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()
      showSuccess(`${amount.toFixed(2)} TL yemek kartı ödemesi eklendi`)

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Yemek kartı ödemesi tamamlandı!')
      } else {
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  function clearPaymentInput() {
    rawPaymentAmount = 0
    updatePaymentDisplay()
    selectedPaymentMethod = ''

    // Clear payment button selections
    document.querySelectorAll('.payment-btn').forEach(btn => {
      btn.classList.remove('is-active', 'is-selected')
    })
  }

  async function handleCardPayment(method) {
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    if (remainingAmount <= 0) {
      showError('Ödeme zaten tamamlanmış!')
      return
    }

    const currentPaymentAmount = rawPaymentAmount / 100 // Convert from cents to TL

    console.log('💳 Kart ödeme işlemi:', {
      method,
      currentPaymentAmount,
      remainingAmount,
      hasKeypadInput: rawPaymentAmount > 0,
    })

    // If no keypad input, use remaining amount
    const paymentAmount = currentPaymentAmount > 0 ? currentPaymentAmount : remainingAmount

    // Validate payment amount
    if (paymentAmount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    try {
      showSuccess(
        `${method === 'credit-card' ? 'Kredi kartı' : 'Yemek kartı'} ile ${paymentAmount.toFixed(2)} TL ödeme POS'a gönderiliyor...`
      )

      // Add partial payment first
      const success = addPartialPayment(method, paymentAmount)

      if (success) {
        // Clear payment method selection and keypad
        selectedPaymentMethod = ''
        rawPaymentAmount = 0
        updatePaymentDisplay()
        document.querySelectorAll('.payment-btn').forEach(btn => {
          btn.classList.remove('is-active', 'is-selected')
        })

        // Send to POS immediately for card payments
        console.log("🏧 Kart ödemesi POS'a gönderiliyor:", {
          method,
          amount: paymentAmount,
          partialPayments,
          isComplete: isPaymentComplete,
        })

        await sendRealSaleData()

        // Check if payment is now complete
        if (isPaymentComplete) {
          showSuccess('Kart ödemesi başarılı! Satış tamamlandı!')
          await completeSale()
        } else {
          showSuccess(`Kart ödemesi başarılı! Kalan: ${remainingAmount.toFixed(2)} TL`)
        }
      }
    } catch (error) {
      console.error('❌ Kart ödeme hatası:', error)
      showError(`Kart ödeme hatası: ${error.message}`)

      // Remove the failed payment from partial payments
      if (partialPayments.length > 0) {
        const lastPayment = partialPayments[partialPayments.length - 1]
        if (lastPayment.method === method && lastPayment.amount === paymentAmount) {
          partialPayments = partialPayments.slice(0, -1)
          totalPaidAmount -= paymentAmount
          showError('Ödeme başarısız oldu, kart ödemesi geri alındı')
        }
      }
    }
  }

  async function handleCashPayment() {
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    if (remainingAmount <= 0) {
      showError('Ödeme zaten tamamlanmış!')
      return
    }

    const currentPaymentAmount = rawPaymentAmount / 100 // Convert from cents to TL

    console.log('💵 Nakit ödeme işlemi:', {
      currentPaymentAmount,
      remainingAmount,
      hasKeypadInput: rawPaymentAmount > 0,
    })

    // If no keypad input, use remaining amount
    const paymentAmount = currentPaymentAmount > 0 ? currentPaymentAmount : remainingAmount

    // Validate payment amount
    if (paymentAmount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    // For cash, open the change calculation modal
    openCashChangeModal()
  }

  // ENHANCED SPLIT PAYMENT FUNCTIONS
  function addPartialPayment(method, amount) {
    // Enhanced validation for payment addition
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return false
    }

    if (amount > remainingAmount) {
      showError(`Kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla ödeme yapılamaz!`)
      return false
    }

    // Check if adding this payment would exceed total amount with tolerance
    if (totalPaidAmount + amount > totalAmount + 0.01) {
      showError(`Bu ödeme toplam tutarı (${totalAmount.toFixed(2)} TL) aşacaktır!`)
      return false
    }

    // Validate payment method
    const validMethods = ['cash', 'credit-card', 'meal-card']
    if (!validMethods.includes(method)) {
      showError('Geçersiz ödeme yöntemi!')
      return false
    }

    // Round amount to prevent floating point issues
    const roundedAmount = Math.round(amount * 100) / 100

    const payment = {
      id: Date.now() + Math.random(), // Ensure unique ID
      method,
      amount: roundedAmount,
      timestamp: new Date().toISOString(),
      methodDisplayName: getPaymentMethodDisplayName(method),
    }

    partialPayments = [...partialPayments, payment]
    totalPaidAmount += amount

    console.log('💰 Kısmi ödeme eklendi:', {
      payment,
      totalPaid: totalPaidAmount,
      remaining: remainingAmount,
      isComplete: isPaymentComplete,
    })

    showSuccess(`${amount.toFixed(2)} TL ${payment.methodDisplayName} ödemesi eklendi`)

    // Clear the payment input
    rawPaymentAmount = 0
    updatePaymentDisplay()

    return true
  }

  function removePartialPayment(paymentId) {
    const payment = partialPayments.find(p => p.id === paymentId)
    if (payment) {
      partialPayments = partialPayments.filter(p => p.id !== paymentId)
      totalPaidAmount -= payment.amount
      showSuccess(`${payment.amount.toFixed(2)} TL ${payment.methodDisplayName} ödemesi kaldırıldı`)
    }
  }

  function clearAllPayments() {
    partialPayments = []
    totalPaidAmount = 0
    showSuccess('Tüm ödemeler temizlendi')
  }

  function getPaymentMethodDisplayName(method) {
    const methodNames = {
      cash: 'Nakit',
      'credit-card': 'Kredi Kartı',
      'meal-card': 'Yemek Kartı',
    }
    return methodNames[method] || method
  }

  // Enhanced payment validation function
  function validatePayments() {
    // Check if there are any items in the cart
    if (salesItems.length === 0) {
      return {
        valid: false,
        message: 'Satış listesi boş! Lütfen önce ürün ekleyin.',
        type: 'warning',
      }
    }

    // Check if there are any payments
    if (partialPayments.length === 0) {
      return {
        valid: false,
        message: 'Hiç ödeme yapılmamış! Lütfen ödeme yöntemi seçin.',
        type: 'info',
      }
    }

    // Calculate totals with high precision
    const totalPaid = partialPayments.reduce((sum, payment) => sum + payment.amount, 0)
    const difference = totalAmount - totalPaid

    // Check for negative amounts
    const invalidPayments = partialPayments.filter(p => p.amount <= 0)
    if (invalidPayments.length > 0) {
      return {
        valid: false,
        message: "Geçersiz ödeme tutarları bulundu! Tüm ödemeler 0'dan büyük olmalıdır.",
        type: 'error',
      }
    }

    // Check if payments are less than transaction amount (with tolerance)
    if (difference > 0.01) {
      return {
        valid: false,
        message: `Ödeme eksik! Kalan tutar: ${difference.toFixed(2)} TL. Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
        type: 'warning',
      }
    }

    // Check if payments exceed transaction amount (with tolerance)
    if (difference < -0.01) {
      return {
        valid: false,
        message: `Ödeme fazla! Fazla tutar: ${Math.abs(difference).toFixed(2)} TL. Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
        type: 'error',
      }
    }

    // Check for duplicate payment IDs (data integrity)
    const paymentIds = partialPayments.map(p => p.id)
    const uniqueIds = new Set(paymentIds)
    if (paymentIds.length !== uniqueIds.size) {
      return {
        valid: false,
        message: 'Ödeme verilerinde tutarsızlık bulundu! Lütfen sayfayı yenileyin.',
        type: 'error',
      }
    }

    // All validations passed
    return {
      valid: true,
      message: `Ödeme doğrulandı! Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
      type: 'success',
    }
  }

  // Legacy function for backward compatibility
  function validateSplitPayments() {
    const result = validatePayments()
    return { valid: result.valid, message: result.message }
  }

  function addQuickPayment(method, amount) {
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    if (amount > remainingAmount) {
      showError(`Kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla ödeme yapılamaz!`)
      return
    }

    const success = addPartialPayment(method, amount)
    if (success) {
      // Clear payment method selection
      selectedPaymentMethod = ''
      document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('is-active', 'is-selected')
      })
    }
  }

  async function makeRequest(deviceIp, path, method, payload, retryCount = 0) {
    const MAX_RETRIES = 3

    console.log(`[POS] Request attempt ${retryCount + 1}/${MAX_RETRIES + 1}`)

    if (retryCount >= MAX_RETRIES) {
      throw new Error('İstek tekrar denemesi başarısız oldu. Lütfen işlemi tekrar deneyiniz.')
    }

    try {
      console.log(`[POS] Making ${method} request to: https://${deviceIp}:4567/${path}`)
      console.log('[POS] Payload:', JSON.stringify(payload, null, 2))

      const response = await window.electronAPI.https.request({
        hostname: deviceIp,
        port: 4567,
        path,
        method,
        payload,
      })

      console.log('[POS] Response:', response)

      if (response.HasError) {
        console.error('[PAVO] Error response:', response)
        console.log('[PAVO] Error Code:', response.ErrorCode, 'Retry Count:', retryCount)

        if (response.ErrorCode === 73) {
          console.log(
            '[PAVO] Sequence error, retrying with new sequence:',
            response.TransactionHandle.TransactionSequence
          )

          // Update global sequence number
          sequenceNumber = response.TransactionHandle.TransactionSequence
          console.log('📊 Global sequenceNumber updated to:', sequenceNumber)

          // Update payload with new sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          // Retry the request
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else if (response.ErrorCode === 72) {
          console.log(
            '[PAVO] Time sync error, retrying with POS time:',
            response.TransactionHandle.TransactionDate
          )
          console.log('[PAVO] Original time was:', payload.Header?.DateTime)
          console.log(
            '[PAVO] Original sequence was:',
            payload.TransactionHandle?.TransactionSequence
          )

          // Update global sequence number from time sync error too
          if (response.TransactionHandle && response.TransactionHandle.TransactionSequence) {
            sequenceNumber = response.TransactionHandle.TransactionSequence
            console.log('📊 Global sequenceNumber updated from time sync:', sequenceNumber)
          }

          // Update payload with POS time and sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          if (payload.Header) {
            payload.Header.DateTime = response.TransactionHandle.TransactionDate
          }

          if (payload.Transaction) {
            payload.Transaction.TransactionDate = response.TransactionHandle.TransactionDate
          }

          console.log('[PAVO] Updated payload for retry:', {
            sequence: payload.TransactionHandle?.TransactionSequence,
            headerTime: payload.Header?.DateTime,
            transactionTime: payload.Transaction?.TransactionDate,
          })

          // Retry the request
          console.log('[PAVO] Starting retry with corrected time...')
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else {
          console.error('PAVO MakeRequest error:', response)
          throw new Error(
            response.Message || 'İstek gönderildi, ama PAVO bilinmeyen bir hata dönüşü yaptı'
          )
        }
      }

      // Başarılı response'larda da sequence güncelle
      if (
        response &&
        response.TransactionHandle &&
        response.TransactionHandle.TransactionSequence
      ) {
        const newSequence = response.TransactionHandle.TransactionSequence
        if (newSequence !== sequenceNumber) {
          console.log("📊 Başarılı response'tan sequence güncelleniyor:", {
            old: sequenceNumber,
            new: newSequence,
            path,
          })
          sequenceNumber = newSequence
        }
      }

      return response
    } catch (error) {
      console.error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu', error)
      throw new Error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu')
    }
  }

  /**
   * Demo satış verisi gönderme fonksiyonu (test amaçlı)
   */
  async function sendDemoSaleData() {
    console.log('🎯 Demo satış verisi gönderiliyor...')

    try {
      // Türkiye saati formatı
      const now = new Date()
      now.setHours(now.getHours() + 3)
      let transactionDate = now.toISOString().split('Z')[0]

      // Limit to 3 digits after decimal point for milliseconds
      const parts = transactionDate.split('.')
      if (parts.length === 2) {
        transactionDate = `${parts[0]}.${parts[1].substring(0, 3)}`
      }

      // TypeScript uyumlu payload yapısı
      const currentSequence = sequenceNumber + 1 // Demo için de sequence'ı 1 artır

      console.log('📊 Demo TransactionSequence kullanılıyor:', {
        current: sequenceNumber,
        sending: currentSequence,
      })

      const demoData = {
        TransactionHandle: {
          SerialNumber: 'PAV600000327',
          TransactionDate: transactionDate,
          TransactionSequence: currentSequence,
          Fingerprint: 'shopigo',
        },
        Sale: {
          RefererApp: 'shopigo',
          RefererAppVersion: '1.0.0',
          OrderNo: `DEMO-${Date.now()}`,
          MainDocumentType: 1, // E_INVOICE
          GrossPrice: 50.0,
          TotalPrice: 50.0,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          SendPhoneNotification: false,
          SendEMailNotification: false,
          NotificationPhone: '',
          DocumentNote: '',
          NotificationEMail: '<EMAIL>',
          ShowCreditCardMenu: false,
          SelectedSlots: ['rf', 'icc', 'manual'],
          EnableAllTerminalsOnRetry: false,
          AllowDismissCardRead: false,
          CardReadTimeout: 30,
          SkipAmountCash: true,
          CancelPaymentLater: true,
          AskCustomer: false,
          SendResponseBeforePrint: false,
          TryAgainOnPaymentFailure: true,
          ReferOtherMediatorsToRetryPayment: true,
          AbandonOptions: {
            IsVoid: true,
            EnableRefundMediatorsOnVoidFailure: true,
          },
          ContinuePaymentWithCardInserted: true,
          HeadUnmaskedCardNumber: 4,
          TailUnmaskedCardNumber: 4,
          ReceiptInformation: {
            ReceiptJsonEnabled: true,
            ReceiptTextEnabled: false,
            ReceiptImageEnabled: false,
            ReceiptWidth: '58mm',
            PrintCustomerReceipt: false,
            PrintMerchantReceipt: false,
            PrintCustomerReceiptCopy: false,
            EnableExchangeRateField: true,
          },
          AddedSaleItems: [
            {
              Name: 'Demo Kahve',
              IsGeneric: true,
              UnitCode: 'C62', // ADET
              TaxGroupCode: 'KDV18',
              ItemQuantity: 2,
              UnitPriceAmount: 15.0,
              GrossPriceAmount: 30.0, // Total for this item (quantity × unit price)
              TotalPriceAmount: 30.0,
              ReservedText: 'DEMO-COFFEE-001',
            },
            {
              Name: 'Demo Sandviç',
              IsGeneric: true,
              UnitCode: 'C62', // ADET
              TaxGroupCode: 'KDV18',
              ItemQuantity: 1,
              UnitPriceAmount: 20.0,
              GrossPriceAmount: 20.0, // Total for this item (quantity × unit price)
              TotalPriceAmount: 20.0,
              ReservedText: 'DEMO-SANDWICH-002',
            },
          ],
          PaymentInformations: [
            {
              Mediator: 2, // Kredi kartı
              Amount: 50.0,
              CurrencyCode: 'TRY',
              ExchangeRate: 1,
              ExternalReferenceText: `demo-ref-${Date.now()}`,
            },
          ],
          AllowedPaymentMediators: [{ Mediator: 2 }, { Mediator: 1 }],
          CustomerParty: {
            CustomerType: 1,
            FirstName: 'Demo',
            MiddleName: 'demo',
            FamilyName: 'Müşteri',
            CompanyName: 'demo',
            TaxOfficeCode: '',
            TaxNumber: '11111111111',
            Phone: '',
            EMail: '',
            Country: 'Türkiye',
            City: 'Istanbul',
            District: '',
            Neighborhood: '',
            Address: '',
          },
          AdditionalInfo: [
            {
              Key: 'ShopigoFurpa',
              Value: 'Demo Satış',
              Print: true,
            },
          ],
        },
      }

      console.log('📤 Demo verisi gönderiliyor:', demoData)
      showSuccess('Demo satış gönderiliyor...')

      const response = await makeRequest('192.168.1.', 'CompleteSale', 'POST', demoData)

      // Demo satış başarılıysa sequence'ı güncelle
      sequenceNumber = currentSequence
      console.log('📊 Demo TransactionSequence güncellendi:', sequenceNumber)

      console.log('✅ Demo satış başarılı:', response)
      showSuccess('Demo satış başarıyla tamamlandı!')

      return response
    } catch (error) {
      console.error('❌ Demo satış hatası:', error)
      showError(`Demo satış hatası: ${error.message}`)
      throw error
    }
  }

  /**
   * Process sale using exact same format as TypeScript version
   */
  async function processSale(saleData) {
    const deviceIp = saleData.deviceIp || '************'
    const serialNumber = saleData.serialNumber || 'PAV600000327'

    try {
      // Create transaction date (same format as TypeScript)
      const now = new Date()
      now.setHours(now.getHours() + 3) // Add 3 hours like in TypeScript
      const transactionDate = now.toISOString().split('Z')[0] // Remove Z

      // Build the exact same payload structure as TypeScript
      const transactionHandle = {
        SerialNumber: serialNumber,
        TransactionDate: transactionDate,
        TransactionSequence: saleData.transactionSequence || 2,
        Fingerprint: 'shopigo',
      }

      const salePayload = {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: `ORDER1-${Date.now()}`, //saleData.orderNo ||
        MainDocumentType: 1, // DocumentType.E_INVOICE
        GrossPrice: saleData.totalPrice,
        TotalPrice: saleData.totalPrice,
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        SendPhoneNotification: false,
        SendEMailNotification: false,
        NotificationPhone: '',
        DocumentNote: '',
        Reserved03: '',
        NotificationEMail: saleData.email || '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: true,
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        ReceiptInformation: {
          ReceiptJsonEnabled: true,
          ReceiptTextEnabled: false,
          ReceiptImageEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: false, // Same as TypeScript
          PrintMerchantReceipt: false, // Same as TypeScript
          PrintCustomerReceiptCopy: false,
          EnableExchangeRateField: true,
        },
        AddedSaleItems: saleData.items.map(item => ({
          Name: item.name,
          IsGeneric: true, // Same as TypeScript
          UnitCode: item.unitCode || 'C62', // ADET
          TaxGroupCode: item.taxGroupCode || 'KDV10',
          ItemQuantity: item.quantity,
          UnitPriceAmount: item.unitPrice || item.price,
          GrossPriceAmount: item.grossPrice || item.unitPrice || item.price,
          TotalPriceAmount: item.totalPrice || item.quantity * (item.unitPrice || item.price),
          ReservedText: item.reservedText || `ITEM-${item.id || Date.now()}`,
          ...(item.discount &&
            item.discount > 0 && {
              PriceEffect: {
                Type: 1, // LineDiscount
                Amount: item.discount,
              },
            }),
        })),
        PaymentInformations: saleData.payments.map(payment => ({
          Mediator: payment.mediator || (payment.method === 'CASH' ? 1 : 2),
          Amount: payment.amount,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          ExternalReferenceText: payment.externalReference || `ref-${Date.now()}`,
        })),
        AllowedPaymentMediators: [
          { Mediator: 2 }, // Card
          { Mediator: 1 }, // Cash
        ],
        CustomerParty: saleData.customer || {
          CustomerType: 1,
          FirstName: 'Demo',
          MiddleName: 'demo',
          FamilyName: 'Customer',
          CompanyName: 'demoe',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Istanbul',
          District: 'Demo',
          Neighborhood: '',
          Address: 'bursa',
        },
        AdditionalInfo: [
          {
            Key: 'Test',
            Value: 'JavaScript Demo',
            Print: true,
          },
        ],
        ...(saleData.discount &&
          saleData.discount > 0 && {
            PriceEffect: {
              Type: 2, // SaleDiscount
              Amount: saleData.discount,
            },
          }),
      }

      const requestPayload = {
        TransactionHandle: transactionHandle,
        Sale: salePayload,
      }

      console.log('[POS] Processing sale with payload:', JSON.stringify(requestPayload, null, 2))

      // Use makeRequest function (same as TypeScript)
      const response = await makeRequest(deviceIp, 'CompleteSale', 'POST', requestPayload)

      if (response.HasError) {
        throw new Error(response.Message)
      }

      const receipt = response.Data

      // Clean up receipt data (same as TypeScript)
      if (receipt.MerchantReceiptJson) {
        delete receipt.MerchantReceiptJson
      }

      if (receipt.CustomerReceiptJson) {
        try {
          const receiptData = JSON.parse(receipt.CustomerReceiptJson)
          receipt.CustomerReceiptJson = {
            ...receiptData,
            customerReceipt1: receiptData.customerReceipt1?.filter(item => item.type !== 'image'),
          }
        } catch (e) {
          console.log('Receipt JSON parse error:', e)
        }
      }

      // Status handling (same as TypeScript)
      const statusMessages = {
        1: 'Satış askıya alındı',
        2: 'Ödeme bekleniyor',
        3: 'Doküman oluşturuluyor',
        4: 'Doküman bekleniyor',
        5: 'Doküman oluşturuldu',
        6: 'Satış başarıyla tamamlandı',
        7: 'Satıştan vazgeçildi',
        8: 'Doküman oluşturulamadı',
        9: 'İşlem imzalanıyor',
        10: 'İmzalama işlemi başarısız oldu',
        11: 'İşlem iptal edildi',
        12: 'Ödemeler iptal ediliyor',
        13: 'Ödemeler iptal edildi',
        14: 'Ödeme iptali gerçekleştirilemedi',
        15: 'Doküman iptal ediliyor',
        16: 'Doküman iptali gerçekleştirilemedi',
        17: 'Doküman iptali bekleniyor',
        18: 'İşlem imzalandı',
        19: 'ERP işlemleri devam ediyor',
        20: 'Doküman onaylandı',
        21: 'Doküman onaylanmadı',
        22: 'İnceleme bekleniyor',
        23: 'Tamamlanmamış satış',
        99: 'Ödeme kaydedici sorunu var',
      }

      const statusId = receipt.StatusId || 99
      const statusMessage = statusMessages[statusId] || 'Ödeme kaydedici sorunu var.'

      return {
        success: [5, 6, 18, 20].includes(statusId), // Successful statuses
        message: statusMessage,
        data: {
          payload: receipt,
          processor_id: 1, // Mock processor ID
        },
      }
    } catch (error) {
      console.error('[POS] Sale processing error:', error)
      return {
        success: false,
        message: error.message || 'Satış işlemi sırasında bir hata oluştu',
        data: null,
      }
    }
  }

  /**
   * Pairing function using https module
   */
  async function pairDevice(deviceConfig) {
    console.log('[DEMO] Starting device pairing...')
    const now = new Date()
    now.setHours(now.getHours() + 3)
    const transactionDate = now.toISOString().split('Z')[0]

    try {
      const transactionHandle = {
        TransactionHandle: {
          SerialNumber: deviceConfig.serialNumber,
          TransactionDate: transactionDate,
          TransactionSequence: 1,
          Fingerprint: 'shopigo',
        },
      }

      console.log('[DEMO] Pairing with payload:', JSON.stringify(transactionHandle, null, 2))

      const response = await makeRequest(
        deviceConfig.ipAddress,
        'Pairing',
        'POST',
        transactionHandle
      )

      console.log('[DEMO] Device paired successfully:', response)
      console.log('[DEMO] Response structure analysis:', {
        hasResponse: !!response,
        responseKeys: response ? Object.keys(response) : null,
        hasTransactionHandle: !!(response && response.TransactionHandle),
        transactionHandleKeys:
          response && response.TransactionHandle ? Object.keys(response.TransactionHandle) : null,
        transactionSequence:
          response && response.TransactionHandle
            ? response.TransactionHandle.TransactionSequence
            : 'NOT_FOUND',
        fullResponse: JSON.stringify(response, null, 2),
      })

      // POS'tan gelen TransactionSequence'ı al ve sakla
      if (
        response &&
        response.TransactionHandle &&
        response.TransactionHandle.TransactionSequence
      ) {
        sequenceNumber = response.TransactionHandle.TransactionSequence
        console.log("📊 POS'tan gelen TransactionSequence:", sequenceNumber)
      } else {
        // Fallback: Pairing başarılıysa 2'den başla
        sequenceNumber = 2
        console.log('📊 Fallback TransactionSequence:', sequenceNumber)
        console.log('📊 Fallback nedeni - Response analizi:', {
          hasResponse: !!response,
          hasTransactionHandle: !!(response && response.TransactionHandle),
          hasSequence: !!(
            response &&
            response.TransactionHandle &&
            response.TransactionHandle.TransactionSequence
          ),
        })
      }

      return {
        success: true,
        message: 'Device paired successfully',
        device: {
          id: Date.now(), // Mock ID
          serialNumber: deviceConfig.serialNumber,
          ipAddress: deviceConfig.ipAddress,
          name: deviceConfig.name,
          isActive: true,
          isPaired: true,
          isDefault: true,
          transactionSequence: sequenceNumber,
        },
      }
    } catch (error) {
      console.error('[DEMO] Pairing error:', error)
      return {
        success: false,
        message: error.message || 'Pairing failed',
        device: null,
      }
    }
  }

  /**
   * Send real sale data to POS terminal using current cart items
   */
  async function sendRealSaleData() {
    if (salesItems.length === 0) {
      throw new Error('Satış listesi boş!')
    }
    pairDevice()

    // Calculate dynamic payment amount based on keypad and remaining amount
    const keypadAmount = rawPaymentAmount / 100 // Convert from cents to TL
    const dynamicPaymentAmount =
      keypadAmount > 0 && keypadAmount <= remainingAmount ? keypadAmount : remainingAmount

    console.log('💰 Dinamik ödeme tutarı hesaplaması:', {
      keypadAmount,
      remainingAmount,
      dynamicPaymentAmount,
      hasKeypadInput: keypadAmount > 0,
      useKeypad: keypadAmount > 0 && keypadAmount <= remainingAmount,
    })

    // Generate unique order number
    const orderNo = `ORDER-${Date.now()}`

    // Generate the current date and time in Turkey timezone (UTC+3)
    const now = new Date()
    now.setHours(now.getHours() + 3)
    let transactionDate = now.toISOString().split('Z')[0]

    // Limit to 3 digits after decimal point for milliseconds
    const parts = transactionDate.split('.')
    if (parts.length === 2) {
      transactionDate = `${parts[0]}.${parts[1].substring(0, 3)}`
    }

    console.log('🕐 Transaction time:', {
      utc: new Date().toISOString(),
      turkey: transactionDate,
      timezone: 'UTC+3',
      format: 'YYYY-MM-DDTHH:mm:ss.fff',
    })

    // Convert salesItems to TypeScript format (AddedSaleItems)
    const addedSaleItems = salesItems.map((item, index) => {
      const quantity = parseFloat(item.quantity || 1)
      const total = parseFloat(item.total || 0)

      // Use 'price' field from salesItems (not 'unitPrice')
      let unitPrice = parseFloat(item.price || 0)

      // Calculate unit price from total if price is missing or 0
      if (unitPrice <= 0 && total > 0 && quantity > 0) {
        unitPrice = total / quantity
      }

      // Ensure minimum unit price (POS requirement)
      if (unitPrice <= 0) {
        unitPrice = 0.01 // Minimum 1 kuruş
      }

      // CRITICAL: Round to 2 decimal places to avoid floating point issues
      unitPrice = Math.round(unitPrice * 100) / 100
      const calculatedTotal = Math.round(quantity * unitPrice * 100) / 100

      // ALTERNATIVE APPROACH: Try different GrossPriceAmount strategies
      // Strategy 1: GrossPrice = TotalPrice (some POS systems expect this)
      // Strategy 2: GrossPrice = UnitPrice (traditional approach)

      // TOGGLE STRATEGY HERE if needed
      const useStrategy1 = true // Change to false to try Strategy 2
      const grossPrice = useStrategy1 ? calculatedTotal : unitPrice

      console.log(`📦 Ürün ${index + 1}:`, {
        name: item.name,
        quantity,
        unitPrice,
        grossPrice,
        calculatedTotal,
        originalTotal: total,
        originalPrice: item.price,
        strategy: useStrategy1 ? 'Strategy1: GrossPrice=Total' : 'Strategy2: GrossPrice=UnitPrice',
        mathCheck: `${quantity} × ${unitPrice} = ${calculatedTotal}`,
        grossCheck: `GrossPrice = ${grossPrice}`,
        validation: useStrategy1
          ? calculatedTotal === grossPrice
            ? '✅'
            : '❌'
          : unitPrice === grossPrice
            ? '✅'
            : '❌',
        posValidation: `Q(${quantity}) × U(${unitPrice}) = T(${calculatedTotal}), G(${grossPrice})`,
      })

      return {
        Name: item.name || `Ürün ${index + 1}`,
        IsGeneric: true,
        UnitCode: 'C62', // ADET
        TaxGroupCode: 'KDV18',
        ItemQuantity: quantity,
        UnitPriceAmount: unitPrice,
        GrossPriceAmount: grossPrice, // Set to calculated total for POS validation
        TotalPriceAmount: calculatedTotal, // Always quantity × unitPrice
        ReservedText: item.inventory_code || item.barcode || item.id || `ITEM-${index + 1}`,
      }
    })

    // Calculate accurate total from AddedSaleItems first
    const calculatedTotalAmount = addedSaleItems.reduce(
      (sum, item) => sum + item.TotalPriceAmount,
      0
    )

    // If payment is partial, scale AddedSaleItems proportionally
    const isPartialPayment = dynamicPaymentAmount < calculatedTotalAmount
    const scaleFactor = isPartialPayment ? dynamicPaymentAmount / calculatedTotalAmount : 1

    console.log('📊 AddedSaleItems ölçeklendirme:', {
      calculatedTotalAmount,
      dynamicPaymentAmount,
      isPartialPayment,
      scaleFactor,
    })

    // Scale AddedSaleItems if partial payment
    const scaledSaleItems = addedSaleItems.map(item => {
      if (!isPartialPayment) return item

      // For partial payment, scale UNIT PRICE not quantity
      // This ensures: quantity × scaledUnitPrice = scaledTotalPrice
      const scaledUnitPrice = Math.round(item.UnitPriceAmount * scaleFactor * 100) / 100
      const scaledTotalPrice = Math.round(item.ItemQuantity * scaledUnitPrice * 100) / 100
      const scaledGrossPrice = scaledTotalPrice // For POS validation

      console.log(`📊 Ürün ölçeklendirme: ${item.Name}`, {
        originalUnitPrice: item.UnitPriceAmount,
        scaledUnitPrice,
        quantity: item.ItemQuantity,
        originalTotal: item.TotalPriceAmount,
        scaledTotal: scaledTotalPrice,
        mathCheck: `${item.ItemQuantity} × ${scaledUnitPrice} = ${scaledTotalPrice}`,
      })

      return {
        ...item,
        UnitPriceAmount: scaledUnitPrice,
        GrossPriceAmount: scaledGrossPrice,
        TotalPriceAmount: scaledTotalPrice,
      }
    })

    // Verify scaled total matches dynamic payment amount
    const scaledTotal = scaledSaleItems.reduce((sum, item) => sum + item.TotalPriceAmount, 0)

    console.log('📊 Ölçeklendirme doğrulama:', {
      originalTotal: calculatedTotalAmount,
      scaledTotal,
      dynamicPaymentAmount,
      difference: Math.abs(scaledTotal - dynamicPaymentAmount),
    })

    // Convert payments to TypeScript format (PaymentInformations) for split payments
    const paymentInformations = []

    // Check if we have split payments (multiple payment methods)
    const hasSplitPayments = partialPayments.length > 1

    console.log('💳 Split payment detection:', {
      partialPaymentsCount: partialPayments.length,
      hasSplitPayments,
      partialPayments: partialPayments.map(p => ({ method: p.method, amount: p.amount })),
    })

    if (hasSplitPayments) {
      // For split payments, include all payment methods with their actual amounts
      const mediatorMap = {
        cash: 1,
        'credit-card': 2,
        'meal-card': 3,
      }

      partialPayments.forEach(payment => {
        paymentInformations.push({
          Mediator: mediatorMap[payment.method] || 2,
          Amount: payment.amount,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          ExternalReferenceText: `ref-${payment.id}`,
        })
      })
    } else {
      // For single payment method, use the current logic with dynamic amount
      const mediatorMap = {
        cash: 1,
        'credit-card': 2,
        'meal-card': 3,
      }

      if (partialPayments.length > 0) {
        const payment = partialPayments[0]
        paymentInformations.push({
          Mediator: mediatorMap[payment.method] || 2,
          Amount: dynamicPaymentAmount,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          ExternalReferenceText: `ref-${payment.id}`,
        })
      }
    }

    // Calculate the correct total amount for POS
    const posTotal = hasSplitPayments
      ? partialPayments.reduce((sum, payment) => sum + payment.amount, 0)
      : dynamicPaymentAmount

    console.log('💳 Ödeme bilgileri güncellemesi:', {
      partialPayments,
      paymentInformations,
      dynamicPaymentAmount,
      totalPaid: totalPaidAmount,
      calculatedTotal: calculatedTotalAmount,
      hasSplitPayments,
      posTotal,
    })

    console.log('💰 Tutar karşılaştırması:', {
      originalTotal: totalAmount,
      calculatedTotal: calculatedTotalAmount,
      difference: Math.abs(totalAmount - calculatedTotalAmount),
      usingCalculatedTotal: calculatedTotalAmount,
    })

    // TypeScript uyumlu payload yapısı
    const currentSequence = sequenceNumber + 1 // Mevcut sequence'ı 1 artır

    console.log('📊 TransactionSequence kullanılıyor:', {
      current: sequenceNumber,
      sending: currentSequence,
    })

    const data = {
      TransactionHandle: {
        SerialNumber: 'PAV600000327',
        TransactionDate: transactionDate,
        TransactionSequence: currentSequence,
        Fingerprint: 'shopigo',
      },
      Sale: {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: orderNo,
        MainDocumentType: 1, // E_INVOICE
        GrossPrice: posTotal, // Use calculated POS total for split payments
        TotalPrice: posTotal, // Use calculated POS total for split payments
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        SendPhoneNotification: false,
        SendEMailNotification: false,
        NotificationPhone: '',
        DocumentNote: '',
        NotificationEMail: '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: true,
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        ReceiptInformation: {
          ReceiptJsonEnabled: true,
          ReceiptTextEnabled: false,
          ReceiptImageEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: false,
          PrintMerchantReceipt: false,
          PrintCustomerReceiptCopy: false,
          EnableExchangeRateField: true,
        },
        AddedSaleItems: scaledSaleItems, // Use scaled items for partial payments
        PaymentInformations: paymentInformations,
        AllowedPaymentMediators: [{ Mediator: 2 }, { Mediator: 1 }],
        CustomerParty: {
          CustomerType: 1,
          FirstName: 'Müşteri',
          MiddleName: 'demo',
          FamilyName: 'demo',
          CompanyName: 'demo',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '5417818194',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Istanbul',
          District: '',
          Neighborhood: '',
          Address: '',
        },
        AdditionalInfo: [
          {
            Key: 'ShopigoFurpa',
            Value: 'Gerçek Satış',
            Print: true,
          },
        ],
      },
    }

    try {
      console.log('📤 Gerçek satış verileri gönderiliyor...', {
        items: salesItems,
        total: totalAmount,
        paymentMethod: selectedPaymentMethod,
        posData: data,
      })
      const response = await makeRequest('************', 'CompleteSale', 'POST', data)

      // Satış başarılıysa sequence'ı güncelle
      sequenceNumber = currentSequence
      console.log('📊 TransactionSequence güncellendi:', sequenceNumber)

      console.log('✅ Gerçek satış başarılı:', response)
      return response
    } catch (error) {
      console.error('❌ Gerçek satış hatası:', error)
      throw error
    }
  }

  /**
   * Send demo sale data (for testing)
   */
  async function sendCompleteSaleData() {
    // Generate the current date and time for TransactionDate in Turkey timezone
    const now = new Date()
    now.setHours(now.getHours() + 3)
    const transactionDate = now.toISOString().split('Z')[0]

    const data = {
      TransactionHandle: {
        SerialNumber: 'PAV600000327',
        TransactionDate: transactionDate,
        TransactionSequence: 2,
        Fingerprint: 'shopigo',
      },
      Sale: {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: '0000000000ABC0006',
        MainDocumentType: 1,
        GrossPrice: 20,
        TotalPrice: 20,
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        PriceEffect: {
          Type: 2,
          Rate: 10,
          Amount: null,
        },
        SendPhoneNotification: false,
        SendEMailNotification: true,
        NotificationPhone: '',
        DocumentNote: '',
        Reserved03: '',
        NotificationEMail: '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: false,
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        AddedSaleItems: [
          {
            Name: 'Gofret',
            IsGeneric: false,
            UnitCode: 'KGM',
            TaxGroupCode: 'KDV10',
            ItemQuantity: 1,
            UnitPriceAmount: 20,
            GrossPriceAmount: 20,
            TotalPriceAmount: 20,
            ReservedText: 'TEST0001',
            PriceEffect: {
              Type: 1,
              Rate: 10,
              Amount: null,
            },
          },
        ],
        PaymentInformations: [
          {
            Mediator: 2,
            Amount: 16.2,
            CurrencyCode: 'TRY',
            ExchangeRate: 1,
            ExternalReferenceText: 'xrkgrtkvr1234',
          },
        ],
        AllowedPaymentMediators: [
          {
            Mediator: 2,
          },
          {
            Mediator: 1,
          },
        ],
        ReceiptInformation: {
          ReceiptImageEnabled: true,
          ReceiptJsonEnabled: false,
          ReceiptTextEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: true,
          PrintCustomerReceiptCopy: true,
          PrintMerchantReceipt: true,
          EnableExchangeRateField: true,
        },
        CustomerParty: {
          CustomerType: 1,
          FirstName: 'John',
          MiddleName: 'sdasds',
          FamilyName: 'Doe',
          CompanyName: '',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '5417818194',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Ankara',
          District: 'Çankaya',
          Neighborhood: '',
          Address: 'bursa bursa',
        },
        AdditionalInfo: [
          {
            Key: 'Test',
            Value: 'Test',
            Print: true,
          },
        ],
        TopPrintableItems: [
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'Ücretsiz Ürünler',
            fontWeight: 'bold',
          },
          {
            type: 'dLine',
            dashWidth: 4,
          },
          {
            type: 'dSpace',
            height: 20,
          },
        ],
        BottomPrintableItems: [
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'Ücretsiz Ürünler',
            fontWeight: 'bold',
          },
          {
            type: 'dLine',
            dashWidth: 4,
          },
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Ketçap',
                offset: 0,
                rightMargin: 134,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Mayonez',
                offset: 0,
                rightMargin: 134,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Kova Boy Patates',
                offset: 0,
                leftMargin: 200,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dLine',
            dashWidth: 15,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'TEK SEFERDE YAPACAĞINIZ 500₺ VE ÜZERİ ALIŞVERİŞLERDE 50₺İNDİRİM SİZLERİ BEKLİYOR',
          },
        ],
      },
    }

    try {
      const response = await makeRequest('************', 'CompleteSale', 'POST', data)
      console.log('Success:', response)
      return response
    } catch (error) {
      console.error('Error sending CompleteSale data:', error)
      throw error
    }
  }

  /**
   * Test functions
   */
  async function testPairing() {
    console.log('=== DEMO PAIRING TEST ===')

    const deviceConfig = {
      serialNumber: 'PAV600000327',
      ipAddress: '************',
      name: 'shopigo',
    }

    const result = await pairDevice(deviceConfig)

    if (result.success) {
      console.log('✅ Pairing successful!')
      console.log('Device info:', result.device)
    } else {
      console.log('❌ Pairing failed:', result.message)
    }

    return result
  }

  /**
   * Test device pairing with detailed logging
   */
  async function testDevicePairing() {
    console.log('🔗 Test POS bağlantısı başlatılıyor...')
    showSuccess('POS cihazı ile bağlantı kuruluyor...')

    try {
      const deviceConfig = {
        serialNumber: 'PAV600000327',
        ipAddress: '************',
        name: 'Test POS Terminal',
      }

      const result = await pairDevice(deviceConfig)

      if (result.success) {
        console.log('✅ POS bağlantısı başarılı!')
        showSuccess(`POS bağlantısı başarılı! Sequence: ${sequenceNumber}`)
      } else {
        console.log('❌ POS bağlantısı başarısız:', result.message)
        showError(`POS bağlantısı başarısız: ${result.message}`)
      }

      return result
    } catch (error) {
      console.error('❌ POS bağlantı hatası:', error)
      showError(`POS bağlantı hatası: ${error.message}`)
      throw error
    }
  }

  function payRemainingAmount() {
    if (remainingAmount <= 0) {
      showError('Kalan tutar bulunmuyor!')
      return
    }

    if (!selectedPaymentMethod) {
      showError('Lütfen ödeme yöntemi seçiniz!')
      return
    }

    console.log('💰 Kalan tutar ödeme:', {
      remainingAmount,
      selectedPaymentMethod,
    })

    // Use the new workflow functions to process the remaining amount
    if (selectedPaymentMethod === 'cash') {
      handleCashPaymentWorkflow(remainingAmount)
    } else if (selectedPaymentMethod === 'credit-card') {
      // Kredi kartı ödemelerini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(remainingAmount)
    } else if (selectedPaymentMethod === 'meal-card') {
      handleMealCardPaymentWorkflow(remainingAmount)
    }
  }

  // Helper function to create new sale record when first item is added
  async function createNewSaleRecord() {
    try {
      console.log('🆕 Creating new sale record for first item...')

      // Validate user authentication
      const userValidation = validateUserAuthentication()
      if (!userValidation.valid) {
        showError(userValidation.error)
        throw new Error(userValidation.error)
      }

      const user = userValidation.user

      // Prepare sale data for in-progress sale creation
      const saleData = {
        employeeUuid: user.uuid || user.id,
        workstationId: 'ws-001', // Default workstation ID
        customerId: null,
      }

      console.log('📊 Creating in-progress sale with data:', saleData)

      // Create in-progress sale record
      const result = await createInProgressSale(saleData)

      if (result.success) {
        currentSaleUuid = result.saleUuid
        isInProgressSale = true

        console.log('✅ New sale record created:', {
          saleUuid: result.saleUuid,
          receiptNumber: result.receiptNumber,
          saleId: result.saleId,
        })

        showInfo(`Yeni satış başlatıldı (${result.receiptNumber})`)
      } else {
        throw new Error('Sale record creation failed')
      }
    } catch (error) {
      console.error('❌ Error creating new sale record:', error)
      showError(`Satış kaydı oluşturulamadı: ${error.message}`)
      throw error
    }
  }

  // Helper function to validate user authentication
  function validateUserAuthentication() {
    let user = null
    const unsubscribe = currentUser.subscribe(value => {
      user = value
    })
    unsubscribe()

    if (!user) {
      return { valid: false, error: 'Kullanıcı oturumu bulunamadı. Lütfen tekrar giriş yapın.' }
    }

    if (!user.id && !user.uuid) {
      return { valid: false, error: 'Kullanıcı bilgileri eksik. Lütfen tekrar giriş yapın.' }
    }

    return { valid: true, user }
  }

  // Helper function to save sale to database
  async function saveSaleToDatabase() {
    try {
      // Get current user from store
      let user = null
      currentUser.subscribe(value => {
        user = value
      })()

      if (!user) {
        throw new Error('Kullanıcı bilgisi bulunamadı')
      }

      // Prepare sale data for database
      const saleData = {
        employeeUuid: user.uuid,
        workstationId: 'ws-001', // Default workstation
        customerId: null,
        discount: 0,
        originalPrice: totalAmount,
        totalPrice: totalAmount,
        items: salesItems.map(item => ({
          inventory_code: item.inventory_code,
          quantity: item.quantity,
          unit_price: item.price,
          total_price: item.total,
          weight: isDecimalUnit(item.unit) ? item.quantity : 0,
          weight_unit: isDecimalUnit(item.unit) ? item.unit : null,
          discount: 0,
        })),
        payments: partialPayments.map(payment => ({
          method: payment.method,
          amount: payment.amount,
        })),
      }

      console.log('💾 Veritabanına kaydedilecek satış verisi:', saleData)

      // Save to database
      const result = await saveSaleTransaction(saleData)

      if (result.success) {
        console.log('✅ Satış veritabanına kaydedildi:', result)
        showSuccess(`Satış kaydedildi! Fiş No: ${result.receiptNumber}`)
        return result
      } else {
        throw new Error(result.message || 'Satış kaydedilemedi')
      }
    } catch (error) {
      console.error('❌ Satış kaydetme hatası:', error)
      showError(`Satış kaydetme hatası: ${error.message}`)
      throw error
    }
  }

  // SALES MANAGEMENT FUNCTIONS
  async function checkForInProgressSale() {
    console.log('  geldi')
    try {
      const inProgressSale = await getTodaysInProgressSale()
      if (inProgressSale) {
        currentSale = inProgressSale
        currentSaleUuid = inProgressSale.uuid
        isInProgressSale = true

        // Convert database items to salesItems format
        if (inProgressSale.items && inProgressSale.items.length > 0) {
          salesItems = inProgressSale.items.map((item, index) => ({
            sequenceNo: index + 1,
            id: item.id,
            name: item.name,
            unit: item.unit || 'adet',
            price: item.price,
            original_price: item.original_price || item.price, // original_price yoksa price kullan
            inventory_code: item.inventory_code,
            barcode: item.barcode || '',
            quantity: item.quantity,
            total: item.total,
          }))
          sequenceNumber = salesItems.length + 1
        }

        showSuccess('Devam eden satış yüklendi')
      }
    } catch (error) {
      console.error('❌ Error checking for in-progress sale:', error)
    }
  }

  async function createNewSaleIfNeeded() {
    if (!currentSaleUuid && !isInProgressSale) {
      try {
        console.log('🆕 Yeni satış oluşturuluyor...')
        const saleData = await createInProgressSale()
        if (saleData) {
          currentSaleUuid = saleData.uuid
          isInProgressSale = true
          console.log('✅ Yeni satış oluşturuldu:', currentSaleUuid)
        }
      } catch (error) {
        console.error('❌ Satış oluşturma hatası:', error)
      }
    }
  }

  async function addItemToCurrentSale(item) {
    if (currentSaleUuid) {
      try {
        console.log('📦 Ürün devam eden satışa ekleniyor...', item)
        await addItemToInProgressSale(currentSaleUuid, {
          inventory_id: item.id,
          quantity: getMinimumQuantity(item.unit || 'adet'),
          unit_price: parseFloat(item.price) || 0,
        })
        console.log('✅ Ürün devam eden satışa eklendi')
      } catch (error) {
        console.error('❌ Ürün ekleme hatası:', error)
      }
    }
  }

  async function completeSale() {
    // Enhanced payment validation
    const validation = validatePayments()

    if (!validation.valid) {
      // Show payment method selection modal if no payments made
      if (partialPayments.length === 0) {
        openPaymentMethodModal()
        return
      }

      // Show appropriate error message based on validation type
      if (validation.type === 'error') {
        showError(validation.message)
      } else if (validation.type === 'warning') {
        showError(validation.message)
      } else {
        showInfo(validation.message)
      }
      return
    }

    // Log successful validation
    console.log('✅ Payment validation passed:', validation.message)

    // If we reach here, payments equal transaction amount (within tolerance)

    try {
      // Check if we have credit card payments that were added without authorization (split payments)
      const creditCardPayments = partialPayments.filter(p => p.method === 'credit-card')

      if (creditCardPayments.length > 0) {
        // For split payments with credit cards, send final authorization
        showInfo("Kredi kartı ödemeleri POS'a gönderiliyor...")
        await sendRealSaleData()
        showSuccess('POS authorization başarılı!')
      }

      // If we have an in-progress sale, complete it
      if (currentSaleUuid && isInProgressSale) {
        await completeInProgressSale(currentSaleUuid, partialPayments)
      } else {
        // Create and save sale transaction
        const salesNumber = await generateSalesNumber()

        // Validate user authentication
        const userValidation = validateUserAuthentication()
        if (!userValidation.valid) {
          showError(userValidation.error)
          return
        }

        const user = userValidation.user
        console.log('👤 Current user validated:', user)

        // Validate user exists in employees table
        let validEmployeeUuid = null
        if (user.uuid) {
          console.log('🔍 Using UUID for employee lookup:', user.uuid)
          validEmployeeUuid = user.uuid
        } else if (user.id) {
          console.log('⚠️ Using ID instead of UUID, need to map to employees table:', user.id)
          // The user.id should correspond to employees.id, and we need the employees.uuid
          // We'll let the main process handle this mapping
          validEmployeeUuid = user.id
        }

        if (!validEmployeeUuid) {
          throw new Error('Valid employee UUID or ID not found')
        }

        // Prepare sale data object for database
        const totalOriginalPrice = salesItems.reduce(
          (sum, item) => sum + (item.original_price || item.price) * item.quantity,
          0
        )

        const saleData = {
          sales_number: salesNumber,
          user_id: user.id || user.uuid,
          employeeUuid: validEmployeeUuid, // Use the validated UUID or ID
          workstationId: 'ws-001', // Varsayılan workstation
          totalPrice: totalAmount,
          originalPrice: totalOriginalPrice, // Main process'te beklenen alan adı
          discount: 0, // İndirim varsa burada hesaplanabilir
          customerId: null, // Müşteri ID'si varsa burada
          total_amount: totalAmount,
          discount_amount: 0, // İndirim varsa burada hesaplanabilir
          tax_amount: 0, // KDV hesabı varsa burada yapılabilir
          status: 4,
          items: salesItems.map(item => ({
            inventory_id: item.id,
            inventory_code: item.inventory_code,
            product_name: item.name,
            unit_price: item.price,
            original_price: item.original_price || item.price,
            quantity: item.quantity,
            unit: item.unit,
            total_price: item.total,
            barcode: item.barcode,
            weight: 0, // Ağırlık varsa burada hesaplanabilir
            weight_unit: null,
            discount: 0, // Ürün bazında indirim varsa burada
          })),
          payments: partialPayments.map(payment => ({
            method: payment.method,
            amount: payment.amount,
            timestamp: payment.timestamp,
          })),
        }
        console.clear()
        console.log(saleData)

        // Validate sale data before saving
        if (!saleData.items || saleData.items.length === 0) {
          throw new Error('Satış ürünleri eksik')
        }

        if (!saleData.payments || saleData.payments.length === 0) {
          throw new Error('Ödeme bilgileri eksik')
        }

        if (saleData.totalPrice <= 0) {
          throw new Error('Geçersiz satış tutarı')
        }

        console.log('💾 Satış verisi veritabanına kaydediliyor:', saleData)
        await saveSaleTransaction(saleData)
      }

      // Reset all sale state
      currentSale = null
      currentSaleUuid = null
      isInProgressSale = false
      salesItems = []
      partialPayments = []
      totalPaidAmount = 0
      sequenceNumber = 1

      showSuccess('Satış başarıyla tamamlandı!')
    } catch (error) {
      console.error('❌ Error completing sale:', error)

      if (error.message.includes('Kullanıcı')) {
        showError(`Kullanıcı hatası: ${error.message}`)
      } else if (error.message.includes('database') || error.message.includes('Database')) {
        showError('Veritabanı hatası. Lütfen tekrar deneyin.')
      } else if (error.message.includes('network') || error.message.includes('Network')) {
        showError('Bağlantı hatası. Lütfen tekrar deneyin.')
      } else {
        showError(`Satış tamamlanamadı: ${error.message}`)
      }
    }
  }

  // INITIALIZE
  onMount(() => {
    // Check for existing in-progress sale
    checkForInProgressSale()

    searchInput?.focus()
    updatePaymentDisplay()

    // CustomerScreen'i aç
    openCustomerScreen()

    // Event listeners
    document.addEventListener('keydown', handleGlobalKeyDown)
    document.addEventListener('click', handleClickOutside)

    return () => {
      if (searchTimeout) clearTimeout(searchTimeout)
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      closeDropdown()
      document.removeEventListener('keydown', handleGlobalKeyDown)
      document.removeEventListener('click', handleClickOutside)
    }
  })
</script>

<div class="sales-page">
  <div class="main-layout">
    <!-- Left Panel: Sales Screen (25%) -->
    <div class="sales-panel">
      <!-- Tab Section -->
      <div class="tab-section">
        <div class="tabs is-boxed">
          <ul>
            <li class={activeTab === 'search' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('search')}
                class="tab-button"
                aria-label="Ürün Ara"
              >
                <span class="icon is-small"><i class="fas fa-search"></i></span>
                <span>Ürün Ara</span>
              </button>
            </li>
            <li class={activeTab === 'food' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('food')}
                class="tab-button"
                aria-label="Gıda"
              >
                <span>Gıda</span>
              </button>
            </li>
            <li class={activeTab === 'grocery' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('grocery')}
                class="tab-button"
                aria-label="Manav"
              >
                <span>Manav</span>
              </button>
            </li>
            <li class={activeTab === 'cleaning' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('cleaning')}
                class="tab-button"
                aria-label="Temizlik"
              >
                <span>Temizlik</span>
              </button>
            </li>
            <li class={activeTab === 'clothing' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('clothing')}
                class="tab-button"
                aria-label="Giyim"
              >
                <span>Giyim</span>
              </button>
            </li>
          </ul>
        </div>

        <div class="tab-content">
          {#if activeTab === 'search'}
            <!-- Search Tab Content -->
            <div class="search-tab-content">
              <div class="field has-addons">
                <div class="control is-expanded has-icons-left autocomplete-container">
                  <input
                    bind:this={searchInput}
                    bind:value={searchTerm}
                    on:input={handleInputChange}
                    on:keydown={handleKeyDown}
                    on:blur={handleInputBlur}
                    class="input is-large"
                    type="text"
                    placeholder="Ürün adı, barkod veya ürün kodu ile arama yapın..."
                    disabled={isSearching}
                    autocomplete="off"
                  />
                  <span class="icon is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
                <div class="control">
                  <button
                    on:click={performSearch}
                    class="button is-primary is-large {isSearching ? 'is-loading' : ''}"
                  >
                    <span class="icon">
                      <i class="fas fa-plus"></i>
                    </span>
                    <span>Ekle</span>
                  </button>
                </div>
              </div>

              <!-- DSL Ürünleri Checkbox -->
              <div class="field" style="margin-top: 1rem;">
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" bind:checked={showDLSProducts} />
                    <span style="margin-left: 0.5rem; font-weight: 500; color: #374151;">
                      DSL ürünleri listele
                    </span>
                  </label>
                </div>
              </div>
            </div>
          {:else}
            <!-- Category Tabs Content (placeholder grid) -->
            <div class="category-tab-content">
              <div class="category-grid">
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 1</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 2</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 3</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 4</span>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <!-- Sales Items Table -->
      <div class="sales-table-section">
        {#if salesItems.length === 0}
          <div class="notification is-light">
            <div class="has-text-centered">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-shopping-basket fa-3x"></i>
              </span>
              <p class="title is-6 has-text-grey">Henüz ürün eklenmedi</p>
              <p class="subtitle is-7 has-text-grey">
                Yukarıdaki arama kutusunu kullanarak ürün ekleyebilirsiniz
              </p>
            </div>
          </div>
        {:else}
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable sales-table">
              <thead>
                <tr>
                  <th class="col-sequence">Sıra</th>
                  <th class="col-product">Ürün</th>
                  <th class="col-unit">Birim</th>
                  <th class="col-price has-text-right">Fiyat</th>
                  <th class="col-quantity">Miktar</th>
                  <th class="col-total has-text-right">Toplam</th>
                  <th class="col-actions">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                {#each salesItems as item, index (item.sequenceNo)}
                  <tr>
                    <td>
                      <span class="tag is-rounded">{item.sequenceNo}</span>
                    </td>
                    <td>
                      <strong>{item.name}</strong>
                      <br />
                      <small class="has-text-grey">
                        {#if item.barcode}
                          | Barkod: {item.barcode}{/if}
                      </small>
                    </td>
                    <td>{item.unit}</td>
                    <td class="has-text-right">
                      <span class="decimal-aligned">
                        {item.price.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </span>
                    </td>
                    <td>
                      <div class="field has-addons quantity-controls">
                        <!-- Decrease Button -->
                        <div class="control">
                          <button
                            on:click={() => decreaseQuantity(index)}
                            class="button is-small quantity-btn decrement"
                            title="Miktarı azalt"
                          >
                            <span class="icon">
                              <i class="fas fa-minus"></i>
                            </span>
                          </button>
                        </div>

                        <!-- Quantity Input -->
                        <div class="control">
                          <input
                            value={formatQuantity(item.quantity)}
                            on:input={event => handleQuantityChange(index, event.target.value)}
                            on:keydown={event => handleQuantityKeydown(event, item.unit)}
                            class="input is-small has-text-centered"
                            type="text"
                            placeholder={isDecimalUnit(item.unit) ? '1,50' : '1'}
                            title={isDecimalUnit(item.unit)
                              ? `${item.unit} birimi için ondalık değer giriniz (örn: 1,50 veya 2,25)`
                              : `${item.unit} birimi için tam sayı giriniz (örn: 1, 2, 3)`}
                            style="width: 80px;"
                          />
                        </div>

                        <!-- Increase Button -->
                        <div class="control">
                          <button
                            on:click={() => increaseQuantity(index)}
                            class="button is-small quantity-btn increment"
                            title="Miktarı artır"
                          >
                            <span class="icon">
                              <i class="fas fa-plus"></i>
                            </span>
                          </button>
                        </div>
                      </div>
                    </td>
                    <td class="has-text-right">
                      <strong class="decimal-aligned">
                        {item.total.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </strong>
                    </td>
                    <td>
                      <button
                        on:click={() => removeItem(index)}
                        class="button is-small is-danger is-outlined"
                        title="Ürünü kaldır"
                      >
                        <span class="icon">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {/if}
      </div>
    </div>

    <!-- Right Panel: POS Payment System (75%) -->
    <div class="content-panel">
      <!-- Payment Summary Header -->
      <div class="payment-header">
        <div class="payment-summary">
          <div class="summary-item">
            <span class="summary-label">İndirim Tutarı</span>
            <span class="summary-value discount">₺ 0</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Ödenen Tutar</span>
            <span class="summary-value paid"
              >{totalPaidAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}</span
            >
          </div>
        </div>
        <div class="total-remaining-container">
          <div class="total-amount-container tags has-addons">
            <span class="tag total-label">Toplam Tutar</span>
            <span class="tag total-amount decimal-aligned">
              {totalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
            </span>
          </div>
          <div class="remaining-amount-container tags has-addons">
            <span class="tag remaining-label">Kalanı Öde</span>
            <button
              class="tag remaining-amount-btn"
              class:active={remainingAmount > 0}
              disabled={remainingAmount <= 0}
              on:click={payRemainingAmount}
            >
              {remainingAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}
            </button>
          </div>
        </div>
      </div>

      <!-- Payment Methods and Keypad -->
      <div class="payment-content">
        <!-- Left Side - Payment Methods -->
        <div class="payment-methods">
          <button class="payment-btn meal-card" on:click={() => selectPaymentMethod('meal-card')}
            >Yemek Kartı</button
          >
          <button
            class="payment-btn cash-drawer"
            on:click={() => selectPaymentMethod('cash-drawer')}>Para Çekmecesini Aç</button
          >

          <button class="payment-btn cash" on:click={() => selectPaymentMethod('cash')}
            >Nakit</button
          >
          <button
            class="payment-btn credit-card"
            on:click={() => selectPaymentMethod('credit-card')}>Kredi Kartı</button
          >

          <button class="complete-sale-btn2" on:click={completeSale}>Satışı Tamamla</button>
        </div>

        <!-- Center - Numeric Keypad -->
        <div class="numeric-keypad">
          <div class="amount-display">
            <input
              type="text"
              class="amount-input"
              bind:value={paymentAmount}
              placeholder="0"
              readonly
            />
          </div>
          <table class="keypad-table">
            <tbody>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('7')}>7</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('8')}>8</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('9')}>9</button></td>
                <td><button class="key-btn backspace" on:click={removeLastDigit}>⌫</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('4')}>4</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('5')}>5</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('6')}>6</button></td>
                <td><button class="key-btn clear" on:click={clearAmount}>C</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('1')}>1</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('2')}>2</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('3')}>3</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('00')}>00</button></td>
              </tr>
              <tr class="keypad-row">
                <td colspan="2">
                  <button class="key-btn zero" on:click={() => addToAmount('0')}>0</button>
                </td>
                <td><button class="key-btn decimal" on:click={() => addToAmount('.')}>.</button></td
                >
                <td></td>
              </tr>
            </tbody>
          </table>

          <!-- Enhanced Split Payments List -->
          {#if partialPayments.length > 0}
            <div class="split-payments-container">
              <div class="split-payments-header">
                <h4 class="split-payments-title">
                  <span class="icon">
                    <i class="fas fa-credit-card"></i>
                  </span>
                  Bölünmüş Ödemeler ({partialPayments.length})
                </h4>
                <div class="payment-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      style="width: {totalAmount > 0 ? (totalPaidAmount / totalAmount) * 100 : 0}%"
                    ></div>
                  </div>
                  <span class="progress-text">
                    {totalPaidAmount.toFixed(2)} / {totalAmount.toFixed(2)} TL
                  </span>
                </div>
              </div>

              <div class="split-payments-list">
                {#each partialPayments as payment (payment.id)}
                  <div class="split-payment-item">
                    <div class="payment-info">
                      <div
                        class="payment-method-badge"
                        class:cash={payment.method === 'cash'}
                        class:credit-card={payment.method === 'credit-card'}
                        class:meal-card={payment.method === 'meal-card'}
                      >
                        <span class="method-icon">
                          {#if payment.method === 'cash'}
                            💵
                          {:else if payment.method === 'credit-card'}
                            💳
                          {:else if payment.method === 'meal-card'}
                            🍽️
                          {/if}
                        </span>
                        <span class="method-name">{payment.methodDisplayName}</span>
                      </div>

                      <div class="payment-amount-display">
                        <span class="amount">{payment.amount.toFixed(2)} TL</span>
                        <div class="payment-actions">
                          <button
                            class="btn-remove"
                            on:click={() => removePartialPayment(payment.id)}
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          {/if}

          <div class="sale-buttons">
            <button class="demo-sale-btn" on:click={sendDemoSaleData}>Demo Satış Test</button>
            <button class="pair-device-btn" on:click={() => testDevicePairing()}>POS Bağla</button>
          </div>
        </div>

        <!-- Right Side - Quick Actions -->
        <div class="quick-actions">
          <button class="action-btn loyalty">Birlik Kart</button>
          <button class="action-btn premium">Premium Kart</button>
          <button class="action-btn gift">Hediye Çeki</button>
          <button class="action-btn receipt">Fiş Beklemeye Al</button>
          <button class="action-btn is-warning">Satış İptal</button>
          <button class="action-btn is-info">Cari Ekle</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Cash Change Modal -->
  {#if showCashChangeModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closeCashChangeModal}
        on:keydown={closeCashChangeModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-money-bill-wave"></i>
            </span>
            Nakit Ödeme
          </p>
          <button class="delete" aria-label="close" on:click={closeCashChangeModal}></button>
        </header>
        <section class="modal-card-body">
          <div class="cash-payment-info">
            <div class="field">
              <label class="label" for="sale-amount">Satış Tutarı</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-large">
                    {remainingAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label" for="cash-received">Alınan Nakit</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-primary is-large">
                    {cashReceived.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            {#if changeAmount > 0}
              <div class="field">
                <label class="label" for="change-amount">Para Üstü</label>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-success is-large">
                      {changeAmount.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY',
                      })}
                    </span>
                  </div>
                </div>
              </div>
            {/if}

            {#if cashReceived < remainingAmount}
              <div class="notification is-danger is-light">
                <span class="icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </span>
                <span>Alınan nakit tutar yetersiz!</span>
              </div>
            {/if}
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-success is-large"
            on:click={completeCashSale}
            disabled={cashReceived < remainingAmount}
          >
            <span class="icon">
              <i class="fas fa-check"></i>
            </span>
            <span>Satışı Tamamla</span>
          </button>
          <button class="button" on:click={closeCashChangeModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}

  <!-- Payment Method Selection Modal -->
  {#if showPaymentMethodModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closePaymentMethodModal}
        on:keydown={closePaymentMethodModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-credit-card"></i>
            </span>
            Ödeme Yöntemi Seçin
          </p>
          <button class="delete" aria-label="close" on:click={closePaymentMethodModal}></button>
        </header>
        <section class="modal-card-body">
          <div class="payment-method-selection">
            <div class="field">
              <label class="label">Toplam Tutar</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-large is-primary">
                    {totalAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label">Ödeme yöntemini seçin:</label>
              <div class="control">
                <div class="payment-method-buttons">
                  <button
                    class="button is-large is-success payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('cash')}
                  >
                    <span class="icon">
                      <i class="fas fa-money-bill-wave"></i>
                    </span>
                    <span>Nakit</span>
                  </button>

                  <button
                    class="button is-large is-info payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('credit-card')}
                  >
                    <span class="icon">
                      <i class="fas fa-credit-card"></i>
                    </span>
                    <span>Kredi Kartı</span>
                  </button>

                  <button
                    class="button is-large is-warning payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('split')}
                  >
                    <span class="icon">
                      <i class="fas fa-divide"></i>
                    </span>
                    <span>Bölünmüş Ödeme</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" on:click={closePaymentMethodModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}
</div>

<style>
  @import './Home.css';

  .payment-method-selection {
    text-align: center;
  }

  .payment-method-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
  }

  .payment-method-option {
    justify-content: flex-start;
    padding: 1.5rem;
  }

  .payment-method-option .icon {
    margin-right: 1rem;
  }
</style>
